export interface Page {
  id: string
  index: number // 实际页码
  type: "cover" | "content" | "back-cover" // 页面类型
  content: PageContent
  thumbnail?: string
  metadata: PageMetadata
}

export interface PageContent {
  stage: {
    attrs: { width: number; height: number; x: number; y: number }
    filters: unknown[]
    className: string
  }
  layer: {
    attrs: { x: number; y: number; width: number; height: number }
    filters: unknown[]
    className: string
  }
  background: {
    image: {
      attrs: { x: number; y: number }
      filters: unknown[]
      className: string
      zIndex: number
    }
    overlay: {
      attrs: { x: number; y: number }
      filters: unknown[]
      className: string
      zIndex: number
    }
  }
  shapes: PageShape[]
}

export interface PageShape {
  attrs: Record<string, unknown>
  filters: unknown[]
  className: string
  zIndex: number
  children?: PageShape[]
}

// 保留旧的接口以兼容现有代码
export interface PageElement {
  id: string
  type: "text" | "image" | "shape" | "group"
  position: { x: number; y: number }
  size: { width: number; height: number }
  rotation?: number
  opacity?: number
  properties: Record<string, unknown>
  zIndex: number
}

export interface BackgroundConfig {
  type: "color" | "gradient" | "image"
  value: string
  opacity?: number
}

export interface PageSettings {
  width: number
  height: number
  padding: { top: number; right: number; bottom: number; left: number }
  bleed?: number
}

export interface PageMetadata {
  createdAt: number
  updatedAt: number
  version: string
}

export interface Album {
  id: string
  name: string
  pages: Page[] // 以页面为中心的数据结构
  currentPageId: string | null // 当前选中的页面ID
  metadata: AlbumMetadata
}

export interface HistoryState {
  id: string
  action:
    | "ADD_PAGE"
    | "INSERT_PAGE_AFTER"
    | "DELETE_PAGE"
    | "REORDER_PAGES"
    | "UPDATE_PAGE"
    | "CREATE_ALBUM"
    | "UPDATE_ALBUM_NAME"
    | "ADD_SPREAD" // @deprecated 保留以兼容旧代码
    | "INSERT_SPREAD_AFTER" // @deprecated 保留以兼容旧代码
    | "DELETE_SPREAD" // @deprecated 保留以兼容旧代码
    | "REORDER_SPREADS" // @deprecated 保留以兼容旧代码
  data: unknown
  timestamp: number
  description: string
}

export interface AlbumMetadata {
  createdAt: number
  updatedAt: number
  version: string
  totalPages: number
  template?: string
  theme?: string
}

// 预览配置接口
export interface PreviewConfig {
  zoom: number
  viewMode: "single" | "spread" | "grid"
  showPageNumbers: boolean
  showGrid: boolean
}

// 相册模板类型
export type AlbumTemplate = "standard" | "premium" | "custom"

// 相册尺寸类型
export type AlbumSize = "small" | "medium" | "large" | "custom"
