import usePikaso from "@/hooks/usePikaso"
import type { Page } from "@/types/album"
import { resize } from "motion"
import { useEffect } from "react"
import { useTitle } from "react-use"

export default function PageThumbnail({ page }: { page?: Page | null }) {
  console.log("🚀 ~ page:", page)
  // 使用传入的页面数据，如果没有则使用默认数据
  const data = page?.content || {}
  console.log("🚀 ~ data:", data)

  useTitle("Pikaso | Core Components | Import/Export")
  const [ref, editor] = usePikaso({
    selection: {
      interactive: false, // 禁用交互式选择
      transformer: {
        borderStroke: "#262626",
        anchorFill: "#262626",
      },
    },
  })

  resize(".canvas-container", (element, { width, height }) => {
    console.log(element, "resize", width, height)

    const newHeight = width * (500 / 500)
    editor?.board.rescale()
    ref.current?.style.setProperty("height", `${newHeight}px`)
  })

  useEffect(() => {
    if (editor) {
      editor.load(JSON.stringify(data))
    }
  }, [editor, data])

  return (
    <div>
      <div ref={ref} className="canvas-container" />
    </div>
  )
}
