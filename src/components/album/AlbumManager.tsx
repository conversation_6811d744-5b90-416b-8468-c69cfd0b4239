import { But<PERSON> } from "@/components/ui/button"
import {
  useAlbumStore,
  useAlbumStoreCanUndoRedo,
  useAlbumStoreUndo,
} from "@/stores/albumStore"
import type { Page } from "@/types/album"
import {
  DndContext,
  type DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  closestCenter,
  useSensor,
  useSensors,
} from "@dnd-kit/core"
import {
  SortableContext,
  horizontalListSortingStrategy,
  sortableKeyboardCoordinates,
  useSortable,
} from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import { useNavigate } from "@tanstack/react-router"
import {
  ArrowLeft,
  Clock,
  Copy,
  Download,
  Edit3,
  Eye,
  HelpCircle,
  Menu,
  Plus,
  RotateCcw,
  RotateCw,
  Save,
  Share2,
  Trash2,
  Upload,
} from "lucide-react"
import type { ReactElement } from "react"
import { useEffect, useRef, useState } from "react"
import { DeleteConfirmDialog } from "./DeleteConfirmDialog"
import PageThumbnail from "./PageThumbnail"

// 封面页面块组件
function CoverPageBlock({
  page,
  onEdit,
  onDelete,
  isDragging,
}: {
  page: Page
  onEdit: (id: string) => void
  onDelete: (id: string) => void
  isDragging: boolean
}) {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200">
      <div className="relative">
        {/* 页面预览区域 */}
        <button
          type="button"
          className="w-full bg-gray-50 p-3 cursor-pointer hover:bg-gray-100 transition-colors relative"
          onClick={() => onEdit(page.id)}
        >
          <div className="grid grid-cols-1 gap-1.5">
            <div className="relative aspect-square bg-white rounded border border-gray-200 overflow-hidden">
              <PageThumbnail page={page} />
              <div className="absolute bottom-1 right-1 text-xs font-medium text-gray-500 bg-white/80 px-1 rounded">
                封面
              </div>
            </div>
          </div>
        </button>
      </div>

      {/* 操作栏 */}
      <div className="p-4 bg-gray-50/50">
        <div className="text-center mb-3">
          <span className="text-lg font-semibold text-gray-800">封面</span>
        </div>

        <div className="flex justify-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onEdit(page.id)}
            className="p-2 text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
            title="编辑封面"
          >
            <Edit3 size={16} />
          </Button>
        </div>
      </div>
    </div>
  )
}

// 封底页面块组件
function BackCoverPageBlock({
  page,
  onEdit,
  onDelete,
  isDragging,
}: {
  page: Page
  onEdit: (id: string) => void
  onDelete: (id: string) => void
  isDragging: boolean
}) {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200">
      <div className="relative">
        {/* 页面预览区域 */}
        <button
          type="button"
          className="w-full bg-gray-50 p-3 cursor-pointer hover:bg-gray-100 transition-colors relative"
          onClick={() => onEdit(page.id)}
        >
          <div className="grid grid-cols-1 gap-1.5">
            <div className="relative aspect-square bg-white rounded border border-gray-200 overflow-hidden">
              <PageThumbnail page={page} />
              <div className="absolute bottom-1 right-1 text-xs font-medium text-gray-500 bg-white/80 px-1 rounded">
                封底
              </div>
            </div>
          </div>
        </button>
      </div>

      {/* 操作栏 */}
      <div className="p-4 bg-gray-50/50">
        <div className="text-center mb-3">
          <span className="text-lg font-semibold text-gray-800">封底</span>
        </div>

        <div className="flex justify-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onEdit(page.id)}
            className="p-2 text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
            title="编辑封底"
          >
            <Edit3 size={16} />
          </Button>
        </div>
      </div>
    </div>
  )
}

/**
 * 计算页面显示页码
 * @param page 页面对象
 * @param spreadIndex 扩展页索引
 * @param isLeftPage 是否为左页
 * @returns 显示的页码字符串，如果不显示页码则返回null
 */
function calculateDisplayPageNumber(
  page: Page | null,
  spreadIndex: number,
  isLeftPage: boolean,
): string | null {
  // 空白页不显示页码
  if (!page) return null

  // 封面和封底保持原有显示
  if (page.type === "cover") return "封面"
  if (page.type === "back-cover") return "封底"

  // 内容页的页码计算
  if (page.type === "content") {
    // 第一个扩展页（spreadIndex = 0）
    if (spreadIndex === 0) {
      // 左页是空白页，不显示页码
      if (isLeftPage) return null
      // 右页显示"1"
      return "1"
    }

    // 后续扩展页的页码计算
    // spreadIndex = 1: 左页显示"2"，右页显示"3"
    // spreadIndex = 2: 左页显示"4"，右页显示"5"
    // 以此类推...
    const basePageNumber = spreadIndex * 2
    return isLeftPage
      ? basePageNumber.toString()
      : (basePageNumber + 1).toString()
  }

  return null
}

// 扩展页面块组件（包含左右两页）
function SpreadPageBlock({
  leftPage,
  rightPage,
  spreadIndex,
  onEdit,
  onDelete,
  onAdd,
  onInsertAfter,
  isDragging,
}: {
  leftPage: Page | null
  rightPage: Page
  spreadIndex: number
  onEdit: (id: string) => void
  onDelete: (id: string) => void
  onAdd: () => void
  onInsertAfter: (pageId: string) => void
  isDragging: boolean
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
  } = useSortable({ id: rightPage.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  // 检查是否为第一个展开页（第一个内容页）
  const isFirstSpread = spreadIndex === 0

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`bg-white rounded-xl shadow-sm border overflow-hidden hover:shadow-md transition-all duration-200 ${
        isSortableDragging
          ? "shadow-lg ring-2 ring-purple-500 scale-105"
          : "border-gray-200"
      }`}
    >
      <div className="relative">
        {/* 拖拽手柄 */}
        <div
          className="absolute top-3 left-3 z-10"
          {...attributes}
          {...listeners}
        >
          <Button
            variant="ghost"
            size="sm"
            className={`p-1.5 bg-white/90 backdrop-blur-sm rounded-md shadow-sm hover:bg-white transition-all ${
              isSortableDragging
                ? "cursor-grabbing scale-110"
                : isDragging
                  ? "cursor-grab ring-2 ring-purple-300 animate-pulse"
                  : "cursor-grab"
            }`}
          >
            <Menu size={14} className="text-gray-600" />
          </Button>
        </div>

        {/* 页面预览区域 */}
        <button
          type="button"
          className="w-full bg-gray-50 p-3 cursor-pointer hover:bg-gray-100 transition-colors relative"
          onClick={() => onEdit(rightPage.id)}
        >
          <div className="grid grid-cols-2 gap-1.5">
            {/* 左页 */}
            <div className="relative aspect-[4/3] bg-white rounded border border-gray-200 overflow-hidden">
              {leftPage ? (
                <>
                  <PageThumbnail page={leftPage} />
                  {calculateDisplayPageNumber(leftPage, spreadIndex, true) && (
                    <div className="absolute bottom-1 right-1 text-xs font-medium text-gray-500 bg-white/80 px-1 rounded">
                      {calculateDisplayPageNumber(leftPage, spreadIndex, true)}
                    </div>
                  )}
                </>
              ) : (
                <div className="flex items-center justify-center h-full bg-gray-50">
                  <span className="text-gray-400 text-xs">空白页</span>
                </div>
              )}
            </div>
            {/* 右页 */}
            <div className="relative aspect-[4/3] bg-white rounded border border-gray-200 overflow-hidden">
              <PageThumbnail page={rightPage} />
              {calculateDisplayPageNumber(rightPage, spreadIndex, false) && (
                <div className="absolute bottom-1 right-1 text-xs font-medium text-gray-500 bg-white/80 px-1 rounded">
                  {calculateDisplayPageNumber(rightPage, spreadIndex, false)}
                </div>
              )}
            </div>
          </div>
        </button>
      </div>

      {/* 操作栏 */}
      <div className="p-4 bg-gray-50/50">
        <div className="flex justify-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onInsertAfter(rightPage.id)}
            className="p-2 text-purple-600 hover:bg-purple-50 rounded-full transition-colors"
            title="在此页后插入新页面"
          >
            <Plus size={16} />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onEdit(rightPage.id)}
            className="p-2 text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
            title="编辑页面"
          >
            <Edit3 size={16} />
          </Button>
          {/* 第一个展开页禁止删除 */}
          {!isFirstSpread && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(rightPage.id)}
              className="p-2 text-red-500 hover:bg-red-50 rounded-full transition-colors"
              title="删除页面"
            >
              <Trash2 size={16} />
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

export function AlbumManager() {
  const navigate = useNavigate()
  const {
    album,
    createAlbum,
    updateAlbumName,
    addPage,
    insertPageAfter,
    deletePage,
    setCurrentPage,
    reorderPages,
    exportAlbum,
    importAlbum,
  } = useAlbumStore()

  // 使用 Zundo 提供的 undo/redo 功能
  const { undo, redo } = useAlbumStoreUndo()
  const { canUndo, canRedo } = useAlbumStoreCanUndoRedo()

  const [deletePageId, setDeletePageId] = useState<string | null>(null)
  const [isSaved, setIsSaved] = useState(true)
  const [isDragging, setIsDragging] = useState(false)
  const [showTemplateSelector, setShowTemplateSelector] = useState(false)
  const [isEditingAlbumName, setIsEditingAlbumName] = useState(false)
  const [albumNameInput, setAlbumNameInput] = useState("")

  // 配置传感器
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  )

  // 初始化相册
  useEffect(() => {
    if (!album) {
      // 如果没有保存的数据，创建新相册（已包含初始页面）
      // persist 中间件会自动处理数据加载
      createAlbum("我的相册")
    }
  }, [album, createAlbum])

  // 数据持久化由 persist 中间件自动处理，无需手动保存
  // 保持 UI 状态显示为已保存
  useEffect(() => {
    setIsSaved(true)
  }, [album])

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case "z":
            event.preventDefault()
            if (event.shiftKey) {
              redo()
            } else {
              undo()
            }
            break
          case "y":
            event.preventDefault()
            redo()
            break
          case "n":
            event.preventDefault()
            handleAddPage()
            break
          case "s":
            event.preventDefault()
            // persist 中间件自动保存，这里只是更新 UI 状态
            setIsSaved(true)
            break
        }
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [undo, redo])

  // 处理拖拽结束
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event
    setIsDragging(false)

    if (over && active.id !== over.id) {
      const oldIndex = album?.pages.findIndex((p) => p.id === active.id) ?? 0
      const newIndex = album?.pages.findIndex((p) => p.id === over.id) ?? 0
      reorderPages(oldIndex, newIndex)
    }
  }

  // 处理添加新页面（在末尾）
  const handleAddPage = () => {
    addPage()
  }

  // 处理在指定页面后插入新页面
  const handleInsertPageAfter = (pageId: string) => {
    insertPageAfter(pageId)
  }

  // 处理删除页面
  const handleDeletePage = (pageId: string) => {
    setDeletePageId(pageId)
  }

  // 确认删除页面
  const confirmDeletePage = () => {
    if (deletePageId) {
      deletePage(deletePageId)
      setDeletePageId(null)
    }
  }

  // 处理页面编辑
  const handleEditPage = (pageId: string) => {
    setCurrentPage(pageId)
    navigate({ to: "/editor" })
  }

  // 处理预览
  const handlePreview = () => {
    navigate({ to: "/preview" })
  }

  // 处理分享
  const handleShare = () => {
    if (navigator.share && album) {
      navigator
        .share({
          title: album.name,
          text: `查看我的相册: ${album.name}`,
          url: window.location.href,
        })
        .catch(console.error)
    } else {
      // 复制链接到剪贴板
      navigator.clipboard
        .writeText(window.location.href)
        .then(() => alert("链接已复制到剪贴板"))
        .catch(() => alert("分享功能暂不可用"))
    }
  }

  // 处理订购
  const handleOrder = () => {
    console.log("Order album", album)
    // 这里可以跳转到订购页面或显示订购对话框
  }

  // 处理导出
  const handleExport = () => {
    const data = exportAlbum()
    if (data) {
      const blob = new Blob([data], { type: "application/json" })
      const url = URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = `${album?.name || "album"}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
  }

  // 处理导入
  const handleImport = () => {
    const input = document.createElement("input")
    input.type = "file"
    input.accept = ".json"
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (file) {
        const reader = new FileReader()
        reader.onload = (e) => {
          const data = e.target?.result as string
          if (importAlbum(data)) {
            alert("相册导入成功")
          } else {
            alert("相册导入失败，请检查文件格式")
          }
        }
        reader.readAsText(file)
      }
    }
    input.click()
  }

  // 创建新相册
  const handleCreateNewAlbum = (name: string) => {
    createAlbum(name)
    setShowTemplateSelector(false)
  }

  // 处理相册名称编辑
  const handleEditAlbumName = () => {
    if (album) {
      setAlbumNameInput(album.name)
      setIsEditingAlbumName(true)
    }
  }

  // 保存相册名称
  const handleSaveAlbumName = () => {
    if (album && albumNameInput.trim()) {
      updateAlbumName(albumNameInput.trim())
      setIsEditingAlbumName(false)
    }
  }

  // 取消编辑相册名称
  const handleCancelEditAlbumName = () => {
    setIsEditingAlbumName(false)
    setAlbumNameInput("")
  }

  // 处理输入框回车和ESC键
  const handleAlbumNameKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSaveAlbumName()
    } else if (e.key === "Escape") {
      handleCancelEditAlbumName()
    }
  }

  // 渲染页面块的函数 - 实现新的展示逻辑
  const renderPageBlocks = () => {
    // 安全检查：确保 album 存在且 pages 是数组
    if (!album || !Array.isArray(album.pages) || album.pages.length === 0)
      return null

    const pages = album.pages
    const blocks: ReactElement[] = []

    // 1. 封面页面块（第一页，type === 'cover'）
    const coverPage = pages.find((page) => page.type === "cover")
    if (coverPage) {
      blocks.push(
        <CoverPageBlock
          key={coverPage.id}
          page={coverPage}
          onEdit={handleEditPage}
          onDelete={handleDeletePage}
          isDragging={isDragging}
        />,
      )
    }

    // 2. 内容页按扩展页显示（左右两页并排）
    const contentPages = pages
      .filter((page) => page.type === "content")
      .sort((a, b) => a.index - b.index)

    // 将内容页按索引成对分组，第一个扩展页的左页为空白（封面背面）
    for (let i = 0; i < contentPages.length; i += 2) {
      const leftPage = i === 0 ? null : contentPages[i] // 第一个扩展页左页为空白
      const rightPage = i === 0 ? contentPages[i] : contentPages[i + 1]

      if (rightPage) {
        blocks.push(
          <SpreadPageBlock
            key={`spread-${leftPage?.id || "empty"}-${rightPage.id}`}
            leftPage={leftPage}
            rightPage={rightPage}
            spreadIndex={Math.floor(i / 2)}
            onEdit={handleEditPage}
            onDelete={handleDeletePage}
            onAdd={handleAddPage}
            onInsertAfter={handleInsertPageAfter}
            isDragging={isDragging}
          />,
        )
      }
    }

    // 3. 封底页面块（最后一页，type === 'back-cover'）
    const backCoverPage = pages.find((page) => page.type === "back-cover")
    if (backCoverPage) {
      blocks.push(
        <BackCoverPageBlock
          key={backCoverPage.id}
          page={backCoverPage}
          onEdit={handleEditPage}
          onDelete={handleDeletePage}
          isDragging={isDragging}
        />,
      )
    }

    return (
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
        onDragStart={() => setIsDragging(true)}
      >
        <SortableContext
          items={pages.map((page) => page.id)}
          strategy={horizontalListSortingStrategy}
        >
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6">
            {blocks}
          </div>
        </SortableContext>
      </DndContext>
    )
  }

  return (
    <div className="min-h-screen bg-gray-100 overflow-x-hidden">
      {/* 顶部导航栏 - 深色主题 */}
      <div className="bg-gray-800 text-white px-4 py-3">
        <div className="max-w-7xl mx-auto">
          {/* 移动端：垂直布局，桌面端：水平布局 */}
          <div className="flex flex-col space-y-3 md:flex-row md:items-center md:justify-between md:space-y-0">
            {/* 左侧：返回按钮和标题 */}
            <div className="flex items-center justify-between md:justify-start md:space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate({ to: "/" })}
                className="flex items-center space-x-2 text-white hover:bg-gray-700"
              >
                <ArrowLeft size={16} />
                <span>返回</span>
              </Button>
              <div className="flex items-center space-x-2 ml-4 md:ml-0">
                {isEditingAlbumName ? (
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={albumNameInput}
                      onChange={(e) => setAlbumNameInput(e.target.value)}
                      onKeyDown={handleAlbumNameKeyDown}
                      onBlur={handleSaveAlbumName}
                      className="bg-gray-700 text-white px-2 py-1 rounded border border-gray-600 focus:border-purple-500 focus:outline-none text-lg font-semibold min-w-[200px]"
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleSaveAlbumName}
                      className="text-green-400 hover:bg-gray-700 p-1"
                    >
                      <Save size={16} />
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-semibold truncate">
                      {album?.name || "我的相册"}
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleEditAlbumName}
                      className="text-gray-300 hover:bg-gray-700 hover:text-white p-1"
                      title="编辑相册名称"
                    >
                      <Edit3 size={16} />
                    </Button>
                  </div>
                )}
              </div>
            </div>

            {/* 右侧：操作按钮 */}
            <div className="flex items-center justify-center space-x-2 md:space-x-3">
              <Button
                variant="outline"
                size="sm"
                onClick={handleShare}
                className="bg-purple-600 text-white border-purple-600 hover:bg-purple-700 flex-1 max-w-[100px] md:flex-none md:max-w-none"
              >
                <Share2 size={16} className="mr-1" />
                <span className="">分享</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleImport}
                className="flex items-center space-x-1 text-gray-600 text-xs md:text-sm px-2 md:px-3"
              >
                <Upload size={14} className="md:w-4 md:h-4" />
                <span>导入</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleExport}
                className="bg-gray-700 text-white border-gray-600 hover:bg-gray-600 flex-1 max-w-[100px] md:flex-none md:max-w-none"
              >
                <Download size={16} className="mr-1" />
                <span className="">导出</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleOrder}
                className="bg-white text-gray-800 hover:bg-gray-100 flex-1 max-w-[100px] md:flex-none md:max-w-none"
              >
                <span className="">订购</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 工具栏 */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="max-w-7xl mx-auto">
          {/* 移动端：垂直布局，桌面端：水平布局 */}
          <div className="flex flex-col space-y-3 md:flex-row md:items-center md:justify-between md:space-y-0">
            {/* 左侧工具组 */}
            <div className="flex flex-col space-y-3 md:flex-row md:items-center md:space-y-0 md:space-x-4">
              {/* 保存状态和历史操作 */}
              <div className="flex items-center justify-between md:justify-start md:space-x-4">
                <div className="flex items-center space-x-2 text-gray-600">
                  <Clock size={16} />
                  <span className="text-sm">
                    {isSaved ? "已保存" : "保存中..."}
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => undo()}
                    disabled={!canUndo}
                    className="p-2"
                    title="撤销 (Ctrl+Z)"
                  >
                    <RotateCcw size={16} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => redo()}
                    disabled={!canRedo}
                    className="p-2"
                    title="重做 (Ctrl+Y)"
                  >
                    <RotateCw size={16} />
                  </Button>
                </div>
              </div>
            </div>

            {/* 右侧操作按钮 */}
            <div className="flex items-center justify-between space-x-2 md:space-x-3">
              <div className="flex items-center space-x-2 md:space-x-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handlePreview}
                  className="flex items-center space-x-1 text-purple-600 text-xs md:text-sm px-2 md:px-3"
                >
                  <Eye size={14} className="md:w-4 md:h-4" />
                  <span>预览</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="max-w-7xl mx-auto px-2 py-4 md:px-4 md:py-6">
        {!album || !Array.isArray(album.pages) || album.pages.length === 0 ? (
          // 空状态
          <div className="text-center py-12">
            <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
              <Plus size={32} className="text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              还没有页面
            </h3>
            <p className="text-gray-500 mb-6">创建您的第一个页面开始设计</p>
            <Button onClick={handleAddPage} size="lg">
              创建第一个页面
            </Button>
          </div>
        ) : (
          // 页面展示区域 - 根据页面类型分组显示
          <div className="space-y-6">{renderPageBlocks()}</div>
        )}
      </div>

      {/* 底部帮助按钮 */}
      <div className="fixed bottom-4 right-4 md:bottom-6 md:right-6">
        <Button
          variant="outline"
          size="sm"
          className="bg-white shadow-lg border-gray-300 text-purple-600 hover:bg-purple-50"
        >
          <HelpCircle size={16} className="mr-2" />
          Help
        </Button>
      </div>

      {/* 拖拽提示 */}
      {isDragging && (
        <div className="fixed top-0 left-0 right-0 bg-purple-600 text-white py-2 text-center z-50">
          现在可以拖拽重新排序页面
        </div>
      )}

      {/* 删除确认对话框 */}
      <DeleteConfirmDialog
        open={deletePageId !== null}
        onOpenChange={(open) => !open && setDeletePageId(null)}
        onConfirm={confirmDeletePage}
        spreadName={
          deletePageId && album
            ? (() => {
                const page = album.pages.find((p) => p.id === deletePageId)
                if (!page) return ""
                if (page.type === "cover") return "封面"
                if (page.type === "back-cover") return "封底"
                return `第 ${page.index} 页`
              })()
            : ""
        }
      />
    </div>
  )
}
